#!/usr/bin/env python3
"""
修复数据库索引问题的脚本
删除有问题的索引，保留数据完整性
"""

import sqlite3
import sys
from pathlib import Path

def fix_database_indexes(db_path: str):
    """修复数据库索引问题"""
    print(f"🔧 开始修复数据库索引: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 查看当前所有索引
        print("\n📋 当前数据库中的索引:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE '%api_logs%';")
        indexes = cursor.fetchall()
        for index in indexes:
            print(f"  - {index[0]}")
        
        # 2. 删除所有api_logs相关的索引，让应用重新创建
        print(f"\n🗑️ 删除所有api_logs相关的索引:")
        for index in indexes:
            index_name = index[0]
            try:
                cursor.execute(f"DROP INDEX IF EXISTS {index_name};")
                print(f"  ✅ 删除索引: {index_name}")
            except sqlite3.Error as e:
                print(f"  ⚠️ 删除索引 {index_name} 时出错: {e}")

        # 3. 删除其他可能冲突的索引
        print(f"\n🗑️ 删除其他可能冲突的索引:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND sql IS NOT NULL;")
        all_indexes = cursor.fetchall()

        # 删除所有非系统索引，让应用重新创建
        for index in all_indexes:
            index_name = index[0]
            if not index_name.startswith('sqlite_'):  # 保留系统索引
                try:
                    cursor.execute(f"DROP INDEX IF EXISTS {index_name};")
                    print(f"  ✅ 删除索引: {index_name}")
                except sqlite3.Error as e:
                    print(f"  ⚠️ 删除索引 {index_name} 时出错: {e}")
        
        # 4. 查看api_logs表结构，确认没有user_id字段
        print(f"\n🔍 检查api_logs表结构:")
        cursor.execute("PRAGMA table_info(api_logs);")
        columns = cursor.fetchall()
        has_user_id = False
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
            if col[1] == 'user_id':
                has_user_id = True

        if has_user_id:
            print("  ⚠️ 发现user_id字段，这可能是问题的根源")
        else:
            print("  ✅ 没有发现user_id字段，这是正确的")

        # 5. 检查剩余的索引
        print(f"\n📋 清理后剩余的索引:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index';")
        remaining_indexes = cursor.fetchall()
        for index in remaining_indexes:
            print(f"  - {index[0]}")
        
        # 6. 提交更改
        conn.commit()
        print(f"\n✅ 数据库索引修复完成!")
        
    except sqlite3.Error as e:
        print(f"❌ 修复过程中出错: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def main():
    """主函数"""
    db_path = "app_system.sqlite3"
    
    if not Path(db_path).exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        sys.exit(1)
    
    # 创建备份
    backup_path = f"{db_path}.fix_backup"
    print(f"📦 创建备份: {backup_path}")
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 备份创建成功")
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        sys.exit(1)
    
    # 修复索引
    if fix_database_indexes(db_path):
        print(f"\n🎉 修复完成! 现在可以重新启动应用程序")
        print(f"💡 如果还有问题，可以从备份恢复: {backup_path}")
    else:
        print(f"\n❌ 修复失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
